import EditorComponent from '@/components/Editor/Editor'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import SortableItem from '@/components/ui/drag-and-drop'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import OrSeparator from '@/components/ui/orSeparator'
import { useToast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import FileService from '@/network/services/file'
import MediaRoomService from '@/network/services/media_room'
import { PageCms, UpdatePageCmsInput, PageBanner } from '@/types/Cms'
import type { UniqueIdentifier } from '@dnd-kit/core'
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { Image } from '@unpic/react'
import { GripHorizontalIcon, MoreHorizontal, Trash2Icon, Monitor, Smartphone } from 'lucide-react'
import React, { FC, useEffect, useState } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useFieldArray, useForm } from 'react-hook-form'
import useSWR, { mutate } from 'swr'

const SLUG = 'career'

type ExtendedPageBanner = PageBanner & {
  id: UniqueIdentifier
}

type MediaFiles = {
  desktop?: FileWithPath & { preview: string }
  mobile?: FileWithPath & { preview: string }
}

const CareerPageCard = () => {
  const { data, isLoading, error } = useSWR(MediaRoomService.findPage(SLUG))

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const career = data.data

  return <CareerPageForm initialValue={career} />
}

const CareerPageForm: FC<{ initialValue: PageCms }> = ({ initialValue }) => {
  const { toast } = useToast()
  const form = useForm<UpdatePageCmsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValue,
      title: initialValue.title ?? '',
      description: initialValue.description ?? '',
      metadata: initialValue.metadata
    }
  })

  const seoThumbnailUrl = form.watch('metadata.seo.thumbnail')
  const seoMedia = form.watch('tmp_seo_media') as (FileWithPath & { preview: string }) | undefined

  // State for sortable banners and media files
  const [sortableBanner, setSortableBanner] = useState<ExtendedPageBanner[]>([])
  const [mediaFiles, setMediaFiles] = useState<Record<string, MediaFiles>>({})

  const {
    fields: userAgreements,
    append: appendAgreement,
    remove: removeAgreement
  } = useFieldArray({ name: 'metadata.user_agreements', control: form.control })

  useEffect(() => {
    form.reset({
      ...initialValue,
      title: initialValue.title ?? '',
      description: initialValue.description ?? '',
      metadata: initialValue.metadata
    })

    // Initialize sortable banners
    if (initialValue.banners) {
      const sortableBanners = initialValue.banners.map((banner, index) => ({
        ...banner,
        id: `banner-${index}` as UniqueIdentifier
      }))
      setSortableBanner(sortableBanners)
    }
  }, [initialValue])

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableBanner((items) => {
        if (!items || !over) {
          return items
        }

        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        const banners = form.getValues('banners') ?? []
        const [banner] = banners.splice(oldIndex, 1)
        banners.splice(newIndex, 0, banner)
        form.setValue('banners', banners)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  const addNewBanner = () => {
    const newId = `banner-${Date.now()}`
    const newBanner: ExtendedPageBanner = {
      id: newId,
      desktop: { name: '', type: 'image' },
      mobile: { name: '', type: 'image' }
    }

    setSortableBanner((prev) => [...prev, newBanner])

    const currentBanners = form.getValues('banners') ?? []
    form.setValue('banners', [...currentBanners, newBanner])
  }

  const handleMediaUpload = (
    bannerId: string,
    device: 'desktop' | 'mobile',
    files: FileWithPath[]
  ) => {
    if (files.length === 0) return

    const file = files[0]
    const preview = Object.assign(file, {
      preview: URL.createObjectURL(file)
    })

    // Update media files state
    setMediaFiles((prev) => ({
      ...prev,
      [bannerId]: {
        ...prev[bannerId],
        [device]: preview
      }
    }))

    // Update banner in sortable state
    setSortableBanner((prev) =>
      prev.map((banner) => {
        if (banner.id === bannerId) {
          return {
            ...banner,
            [device]: {
              ...banner[device],
              name: file.name,
              type: file.type.split('/')[0] as 'video' | 'image',
              media_file: preview
            }
          }
        }
        return banner
      })
    )

    // Update form values
    const currentBanners = form.getValues('banners') ?? []
    const updatedBanners = currentBanners.map((banner, index) => {
      if (sortableBanner[index]?.id === bannerId) {
        return {
          ...banner,
          [device]: {
            ...banner[device],
            name: file.name,
            type: file.type.split('/')[0] as 'video' | 'image',
            media_file: preview
          }
        }
      }
      return banner
    })
    form.setValue('banners', updatedBanners)
  }

  const removeBanner = (bannerId: string) => {
    setSortableBanner((prev) => prev.filter((banner) => banner.id !== bannerId))

    const bannerIndex = sortableBanner.findIndex((banner) => banner.id === bannerId)
    const currentBanners = form.getValues('banners') ?? []
    currentBanners.splice(bannerIndex, 1)
    form.setValue('banners', currentBanners)

    // Clean up media files
    setMediaFiles((prev) => {
      const newMediaFiles = { ...prev }
      delete newMediaFiles[bannerId]
      return newMediaFiles
    })
  }

  const removeMedia = (bannerId: string, device: 'desktop' | 'mobile') => {
    // Update media files state
    setMediaFiles((prev) => ({
      ...prev,
      [bannerId]: {
        ...prev[bannerId],
        [device]: undefined
      }
    }))

    // Update banner in sortable state
    setSortableBanner((prev) =>
      prev.map((banner) => {
        if (banner.id === bannerId) {
          return {
            ...banner,
            [device]: {
              ...banner[device],
              name: '',
              url: undefined,
              media_file: undefined
            }
          }
        }
        return banner
      })
    )

    // Update form values
    const currentBanners = form.getValues('banners') ?? []
    const updatedBanners = currentBanners.map((banner, index) => {
      if (sortableBanner[index]?.id === bannerId) {
        return {
          ...banner,
          [device]: {
            ...banner[device],
            name: '',
            url: undefined,
            media_file: undefined
          }
        }
      }
      return banner
    })
    form.setValue('banners', updatedBanners)
  }

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // Collect all files that need to be uploaded
      const filesToUpload: File[] = []
      const uploadMapping: Array<{
        bannerIndex: number
        device: 'desktop' | 'mobile'
        fileIndex: number
      }> = []

      values.banners?.forEach((banner, bannerIndex) => {
        ;['desktop', 'mobile'].forEach((device) => {
          const deviceData = banner[device as 'desktop' | 'mobile']
          if (deviceData?.media_file && !deviceData.url) {
            uploadMapping.push({
              bannerIndex,
              device: device as 'desktop' | 'mobile',
              fileIndex: filesToUpload.length
            })
            filesToUpload.push(deviceData.media_file as File)
          }
        })
      })

      // Upload files if any
      if (filesToUpload.length > 0) {
        const { data: mediaResponse } = await FileService.upload(filesToUpload)

        // Update banner URLs with uploaded URLs
        uploadMapping.forEach(({ bannerIndex, device, fileIndex }) => {
          if (values.banners![bannerIndex][device]) {
            values.banners![bannerIndex][device]!.url = mediaResponse.uploads[fileIndex].url
            // Remove media_file before sending to server
            delete values.banners![bannerIndex][device]!.media_file
          }
        })
      }

      // Handle SEO media upload
      if (values.tmp_seo_media && !values.metadata?.seo?.thumbnail) {
        const { data: mediaResponse } = await FileService.upload([values.tmp_seo_media] as File[])
        if (!values.metadata) values.metadata = { seo: {} }
        if (!values.metadata.seo) values.metadata.seo = {}
        values.metadata.seo.thumbnail = mediaResponse.uploads[0].url
      }
      delete values.tmp_seo_media

      console.log('submit values: ', values)

      const { data: response } = await MediaRoomService.updatePage(initialValue.id, values)

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findPage(SLUG)))
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Career Page</CardTitle>
        <Button
          type="submit"
          form="career-form"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
          className="gap-1"
        >
          Save
          {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
        </Button>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id="career-form" onSubmit={onSubmit} className="space-y-4">
            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="title"
                rules={{ required: 'Please enter the title' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title*</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="description"
                rules={{ required: 'Please enter the description' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description*</FormLabel>
                    <FormControl>
                      <EditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="banners"
              render={() => (
                <>
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Primary Banners</FormLabel>
                      <Button type="button" variant="outline" size="sm" onClick={addNewBanner}>
                        Add Banner
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>

                  {sortableBanner.length > 0 && (
                    <div className="mt-2 flex flex-col space-y-6">
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                      >
                        <SortableContext
                          items={sortableBanner}
                          strategy={verticalListSortingStrategy}
                        >
                          {sortableBanner.map((banner, index) => (
                            <div key={banner.id} className="border rounded-lg p-4 bg-gray-50">
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-2">
                                  <SortableItem id={banner.id} className="cursor-grab">
                                    <GripHorizontalIcon size={16} />
                                  </SortableItem>
                                  <h4 className="font-medium">Banner {index + 1}</h4>
                                </div>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <span className="sr-only">Open menu</span>
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() => removeBanner(banner.id as string)}
                                      className="space-x-2 text-red-600"
                                    >
                                      <Trash2Icon size="16" />
                                      <span>Delete Banner</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Desktop Upload */}
                                <div className="space-y-3">
                                  <div className="flex items-center gap-2">
                                    <Monitor size={16} />
                                    <Label className="font-medium">Desktop</Label>
                                  </div>

                                  {!banner.desktop?.url &&
                                  !mediaFiles[banner.id as string]?.desktop ? (
                                    <Dropzone
                                      accept={{
                                        'image/*': ['.jpeg', '.jpg', '.png'],
                                        'video/*': ['.mp4']
                                      }}
                                      description="Drop desktop asset here"
                                      multiple={false}
                                      onDrop={(files) =>
                                        handleMediaUpload(banner.id as string, 'desktop', files)
                                      }
                                    />
                                  ) : (
                                    <div className="space-y-2">
                                      {banner.desktop?.type === 'image' && (
                                        <Image
                                          src={
                                            mediaFiles[banner.id as string]?.desktop?.preview ||
                                            banner.desktop?.url ||
                                            ''
                                          }
                                          layout="constrained"
                                          width={200}
                                          height={120}
                                          className="rounded-md border"
                                        />
                                      )}
                                      {banner.desktop?.type === 'video' && (
                                        <video
                                          src={
                                            mediaFiles[banner.id as string]?.desktop?.preview ||
                                            banner.desktop?.url ||
                                            ''
                                          }
                                          className="rounded-md border w-full max-w-[200px] h-[120px] object-cover"
                                        />
                                      )}

                                      <div className="flex items-center justify-between">
                                        <div>
                                          <Label className="text-xs font-medium">
                                            {banner.desktop?.url
                                              ? 'Uploaded'
                                              : banner.desktop?.name}
                                          </Label>
                                          {!banner.desktop?.url &&
                                            mediaFiles[banner.id as string]?.desktop && (
                                              <Label className="text-xs text-gray-500 block">
                                                {bytesToSize(
                                                  mediaFiles[banner.id as string]?.desktop?.size ||
                                                    0
                                                )}
                                              </Label>
                                            )}
                                        </div>
                                        <Button
                                          type="button"
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            removeMedia(banner.id as string, 'desktop')
                                          }
                                        >
                                          <Trash2Icon size={14} />
                                        </Button>
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Mobile Upload */}
                                <div className="space-y-3">
                                  <div className="flex items-center gap-2">
                                    <Smartphone size={16} />
                                    <Label className="font-medium">Mobile</Label>
                                  </div>

                                  {!banner.mobile?.url &&
                                  !mediaFiles[banner.id as string]?.mobile ? (
                                    <Dropzone
                                      accept={{
                                        'image/*': ['.jpeg', '.jpg', '.png'],
                                        'video/*': ['.mp4']
                                      }}
                                      description="Drop mobile asset here"
                                      multiple={false}
                                      onDrop={(files) =>
                                        handleMediaUpload(banner.id as string, 'mobile', files)
                                      }
                                    />
                                  ) : (
                                    <div className="space-y-2">
                                      {banner.mobile?.type === 'image' && (
                                        <Image
                                          src={
                                            mediaFiles[banner.id as string]?.mobile?.preview ||
                                            banner.mobile?.url ||
                                            ''
                                          }
                                          layout="constrained"
                                          width={120}
                                          height={200}
                                          className="rounded-md border"
                                        />
                                      )}
                                      {banner.mobile?.type === 'video' && (
                                        <video
                                          src={
                                            mediaFiles[banner.id as string]?.mobile?.preview ||
                                            banner.mobile?.url ||
                                            ''
                                          }
                                          className="rounded-md border w-full max-w-[120px] h-[200px] object-cover"
                                        />
                                      )}

                                      <div className="flex items-center justify-between">
                                        <div>
                                          <Label className="text-xs font-medium">
                                            {banner.mobile?.url ? 'Uploaded' : banner.mobile?.name}
                                          </Label>
                                          {!banner.mobile?.url &&
                                            mediaFiles[banner.id as string]?.mobile && (
                                              <Label className="text-xs text-gray-500 block">
                                                {bytesToSize(
                                                  mediaFiles[banner.id as string]?.mobile?.size || 0
                                                )}
                                              </Label>
                                            )}
                                        </div>
                                        <Button
                                          type="button"
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => removeMedia(banner.id as string, 'mobile')}
                                        >
                                          <Trash2Icon size={14} />
                                        </Button>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </SortableContext>
                      </DndContext>
                    </div>
                  )}
                </>
              )}
            />

            <div className="grid gap-3">
              {userAgreements.map((item, i) => {
                return (
                  <React.Fragment key={item.id}>
                    {i > 0 && <FormItem className="flex flex-col col-span-2"></FormItem>}
                    <FormLabel className="flex gap-2">
                      <span>User Agreement {i + 1}</span>
                      {userAgreements.length > 1 && (
                        <Trash2Icon size={16} color="red" onClick={() => removeAgreement(i)} />
                      )}
                    </FormLabel>
                    <FormField
                      control={form.control}
                      name={`metadata.user_agreements.${i}`}
                      rules={{ required: 'This is required' }}
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-col col-span-2">
                            <EditorComponent content={field.value} onChange={field.onChange} />
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </React.Fragment>
                )
              })}
              <FormItem className="flex flex-col col-span-2">
                <Button type="button" onClick={() => appendAgreement('')}>
                  Add User Agreement
                </Button>
              </FormItem>
            </div>

            <FormField
              control={form.control}
              name="metadata.seo.title"
              // rules={{ required: 'Please enter the SEO title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO Title</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.seo.description"
              // rules={{ required: 'Please enter the SEO description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO Description</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.seo.thumbnail"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>SEO Thumbnail</FormLabel>
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    <Input placeholder="Image Url" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <OrSeparator />

            <FormField
              control={form.control}
              name="tmp_seo_media"
              render={({ field }) => (
                <>
                  <FormItem>
                    <FormLabel>Upload Image</FormLabel>
                    <FormControl>
                      <Dropzone
                        accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                        description="Drop your image here, or click to browse"
                        multiple={false}
                        onDrop={(acceptedFiles) => {
                          const file = acceptedFiles[0]
                          const exist = seoMedia?.path === file.path

                          if (!exist) {
                            Object.assign(file, {
                              preview: URL.createObjectURL(file)
                            })
                          }

                          form.setValue('tmp_seo_media', file)
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  {(seoThumbnailUrl || seoMedia?.preview) && (
                    <div className="mt-2 flex flex-col space-y-4">
                      <Image
                        src={seoMedia?.preview ?? seoThumbnailUrl ?? ''}
                        layout="constrained"
                        className="rounded-md"
                        width={320}
                        height={320}
                      />
                    </div>
                  )}
                </>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default CareerPageCard
