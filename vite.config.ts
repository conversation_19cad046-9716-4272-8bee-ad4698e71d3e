import react from '@vitejs/plugin-react-swc'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { PluginOption, defineConfig, splitVendorChunkPlugin } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: true,
    port: 3000
  },
  plugins: [react(), splitVendorChunkPlugin(), visualizer() as PluginOption],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks(id: string) {
          // creating a chunk to @open-ish deps. Reducing the vendor chunk size
          // if (id.includes("@open-ish") || id.includes("tslib")) {
          //   return "@open-ish";
          // }
          // if (id.includes("@radix-ui")) {
          //   return "@radix-ui";
          // }
          if (id.includes('luxon')) {
            return '@luxon'
          }
          if (id.includes('date-fns')) {
            return '@date-fns'
          }
          if (id.includes('@tanstack')) {
            return '@tanstack'
          }
          // if (id.includes('react-hook-form')) {
          //   return '@react-hook-form'
          // }
          if (id.includes('tailwind')) {
            return '@tailwind'
          }
          if (id.includes('axios')) {
            return '@axios'
          }
          // creating a chunk to react routes deps. Reducing the vendor chunk size
          if (
            id.includes('react-router-dom') ||
            id.includes('@remix-run') ||
            id.includes('react-router')
          ) {
            return '@react-router'
          }
        }
      }
    }
  }
})
